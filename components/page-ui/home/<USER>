"use client";

import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Star, Heart } from "lucide-react";

const HomePage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="relative w-full h-auto">
        <Image
          src="/home-page/hero-cover.png"
          alt="Pets - Cat, Dog, and Rabbit"
          width={1440}
          height={522}
          sizes="(max-width: 768px) 100vw,
              (max-width: 1200px) 50vw,
              33vw"
          style={{ height: "100%", width: "100%" }}
        />
      </section>

      {/* AI Doctor Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            <PERSON><PERSON>ch sử dụng AI doctor hiệu quả
          </h2>
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Video Section */}
            <div className="relative">
              <div className="bg-gray-800 rounded-lg aspect-video flex items-center justify-center relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-yellow-400 to-orange-500 opacity-80"></div>
                <div className="relative z-10 text-center text-white">
                  <div className="mb-4">
                    <span className="text-6xl">🐕</span>
                  </div>
                  <h3 className="text-2xl font-bold mb-2">PETLINGO AI</h3>
                  <p className="text-lg mb-4">TALK TO YOUR PET WITH AI!</p>
                  <Button
                    size="lg"
                    className="rounded-full bg-white text-gray-800 hover:bg-gray-100"
                  >
                    <Play className="w-5 h-5 mr-2" />
                    Play Video
                  </Button>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="space-y-6">
              {[
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                "Lorem ipsum dolor",
                "Lorem ipsum dolor",
                "Lorem ipsum dolor",
              ].map((feature, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center flex-shrink-0 mt-1">
                    {index + 1}
                  </div>
                  <p className="text-gray-700 text-lg">{feature}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-center space-x-6">
            {[
              { name: "Food", icon: "🥫" },
              { name: "Accessories", icon: "🎾" },
              { name: "Pet shop", icon: "🛍️" },
              { name: "Treat/Complementary", icon: "🍖" },
            ].map((category) => (
              <Button
                key={category.name}
                variant="outline"
                className="h-16 px-8 rounded-full border-2 border-green-500 text-green-700 hover:bg-green-50"
              >
                <span className="text-2xl mr-3">{category.icon}</span>
                {category.name}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Hot Deals Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center mb-12">
            <span className="text-4xl mr-4">🔥</span>
            <h2 className="text-3xl font-bold text-gray-800">
              Hot deal hôm nay
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-4">
                  <div className="relative mb-4">
                    <div className="bg-gray-200 rounded-lg aspect-square flex items-center justify-center">
                      <span className="text-4xl">
                        {["🦴", "💋", "🥫", "🧸"][index]}
                      </span>
                    </div>
                    <Badge className="absolute top-2 left-2 bg-yellow-400 text-black">
                      $
                    </Badge>
                    <button className="absolute top-2 right-2 p-1 rounded-full bg-white shadow-md">
                      <Heart className="w-4 h-4 text-gray-400" />
                    </button>
                  </div>
                  <div className="space-y-2">
                    <div className="flex text-yellow-400 text-sm">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-current" />
                      ))}
                    </div>
                    <h3 className="font-semibold text-gray-800">
                      Angel's Bowl/Rectangle Smart & Stay Active Gum
                    </h3>
                    <div className="flex items-center space-x-2">
                      <span className="text-red-500 line-through text-sm">
                        25.000₫
                      </span>
                      <span className="font-bold text-lg">23.000₫</span>
                    </div>
                    <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black">
                      Add to cart
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            Dịch vụ thú cưng gần bạn
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              { name: "Veterinary", icon: "👩‍⚕️", color: "bg-blue-500" },
              { name: "Grooming", icon: "✂️", color: "bg-blue-500" },
              { name: "Hotel", icon: "🏨", color: "bg-blue-500" },
              { name: "Pet shop", icon: "🛍️", color: "bg-blue-500" },
            ].map((service) => (
              <Card
                key={service.name}
                className={`${service.color} text-white hover:shadow-lg transition-shadow cursor-pointer`}
              >
                <CardContent className="p-8 text-center">
                  <div className="text-6xl mb-4">{service.icon}</div>
                  <h3 className="text-xl font-semibold">{service.name}</h3>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Adoption Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            Ready for adoption
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { name: "Zong", age: "1 tuổi", gender: "Đực", type: "Chó" },
              { name: "Siêng", age: "6 tháng", gender: "Cái", type: "Mèo" },
              { name: "Ba", age: "1 tuổi", gender: "Đực", type: "Chó" },
            ].map((pet, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0">
                  <div className="relative">
                    <div className="bg-gray-200 rounded-t-lg aspect-square flex items-center justify-center">
                      <span className="text-8xl">
                        {["🐕", "🐱", "🐕"][index]}
                      </span>
                    </div>
                    <button className="absolute top-4 right-4 p-2 rounded-full bg-white shadow-md">
                      <Heart className="w-5 h-5 text-gray-400" />
                    </button>
                  </div>
                  <div className="p-4">
                    <h3 className="font-bold text-xl mb-2">{pet.name}</h3>
                    <div className="text-gray-600 space-y-1 mb-4">
                      <p>
                        {pet.type} • {pet.age} • {pet.gender}
                      </p>
                    </div>
                    <Button className="w-full bg-yellow-400 hover:bg-yellow-500 text-black">
                      Nhận nuôi
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* News Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-12">
            <h2 className="text-3xl font-bold text-gray-800">
              Tin tức mới nhất từ Pawhub
            </h2>
            <Button variant="outline">Xem thêm →</Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <Card
                key={index}
                className="group hover:shadow-lg transition-shadow"
              >
                <CardContent className="p-0">
                  <div className="bg-gray-200 rounded-t-lg aspect-video flex items-center justify-center">
                    <span className="text-4xl">📰</span>
                  </div>
                  <div className="p-6">
                    <h3 className="font-bold text-lg mb-2">
                      Pellentesque cursus
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      Aliquam Ut This Ligature • 6 tháng 9, 2024
                    </p>
                    <p className="text-gray-700 line-clamp-3">
                      Mauris mattis dui ante, at vehicula massa bibendum et.
                      Pellentesque cursus imperdiet lorem, non lacinia nunc
                      rhoncus vitae.
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
